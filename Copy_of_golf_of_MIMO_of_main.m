%% 清空工作空间和命令窗口，关闭所有打开的图形窗口
close all;
clearvars;
clc;

%% 构造MIMO-FMCW雷达

fmcw = MIMORADAR;

%% 物理场景
fmcw.propagationSpeed = physconst('LightSpeed');% 调取光速在空气中的常量值

%% FMCW波形参数设计
fmcw.radarFrequency = 77e9; %载频60Ghz
fmcw.chirpTRandEndime = 56e-6;% 线性调频上升时间  
fmcw.Tidle = 14e-6; %chirp 空闲时间 200e-6
fmcw.sweepBw = 1e12;% 调频斜率200MHZ/us
fmcw.Bw = fmcw.chirpTRandEndime * fmcw.sweepBw;% 调频带宽3.8GHz
fmcw.NumOfChirpInOneFrame = 64;%每帧有64个chirp
fmcw.TimeOfFrame = 40e-3; %每帧40ms
fmcw.activateTimeInOneFrame = (fmcw.chirpTRandEndime + fmcw.Tidle)* fmcw.NumOfChirpInOneFrame;
fmcw.TimeOfFrame = fmcw.activateTimeInOneFrame; %每帧40ms
% active时间 一般不能超过50%帧时间
if((fmcw.activateTimeInOneFrame/fmcw.TimeOfFrame)>0.5)
    warning('帧持续时间超过50%帧总时间');
end

%% 天线参数设计
fmcw.numberOfTransmitAntennas = 2; % 发射天线个数
fmcw.numberOfReceiverAntennas = 4; % 接受天线个数
fmcw.numberOfvirtueRxannate = fmcw.numberOfTransmitAntennas * fmcw.numberOfReceiverAntennas ;% 虚拟天线数目
fmcw.lambda = fmcw.propagationSpeed/fmcw.radarFrequency; % 波长
% fmcw.ApertureD = fmcw.lambda/2 * fmcw.numberOfvirtueRxannate; %孔径大小
if strcmp(fmcw.MIMOTRmode, 'TDM-MIMO')
%     fmcw.chirpTRandEndime =  fmcw.chirpTRandEndime * fmcw.numberOfTransmitAntennas;
%     fmcw.activateTimeInOneFrame = (fmcw.chirpTRandEndime * fmcw.numberOfTransmitAntennas+ fmcw.Tidle)  * fmcw.NumOfChirpInOneFrame;

    fmcw.activateTimeInOneFrame = (fmcw.chirpTRandEndime+ fmcw.Tidle) * fmcw.numberOfTransmitAntennas * fmcw.NumOfChirpInOneFrame;
    if((fmcw.activateTimeInOneFrame/fmcw.TimeOfFrame)>0.5)
        warning('在OOB固件中最好不要超过50%，但是目前也能用。帧持续时间超过50%帧总时间');
    end
end


% Array 
%                                      2*lambda
% RX4<-->RX3<-->RX2<-->RX1<----->TX1<------------>TX2

% 天线阵列
tx2Pos = [0, 0, 0] .* fmcw.lambda;
tx1Pos = [-2, 0, 0] .* fmcw.lambda;

rx_offset = [-3 * fmcw.lambda, 0, 0];
rx4Pos = [0, 0, 0] .* fmcw.lambda/2 + rx_offset;
rx3Pos = [-1, 0, 0] .* fmcw.lambda/2 + rx_offset;
rx2Pos = [-2, 0, 0] .* fmcw.lambda/2 + rx_offset;
rx1Pos = [-3, 0, 0] .* fmcw.lambda/2 + rx_offset;

fmcw.txPos = [tx1Pos; tx2Pos];
fmcw.rxPos = [rx1Pos; rx2Pos; rx3Pos; rx4Pos];


%% ADC采样参数设计
fmcw.adcStartTime = 2e-6;% adc启动时间
fmcw.adcSamplingFrequency = 5e6;% adc采样频率
fmcw.numadcInPerChirp   = 268; % adc点数

if (fmcw.numadcInPerChirp>=((fmcw.chirpTRandEndime-fmcw.adcStartTime)*fmcw.adcSamplingFrequency))
    error('采样点数超过帧长度');
end

fig = figure(1);
% 设置整个窗口背景颜色为纯白
set(fig, 'Color', 'w');
ax1 = axes('Parent', fig);
hold(ax1, 'on') % 将绘图保持在同一个图中
title(ax1, 'Angle-Time', 'FontSize', 14)

%画真值
h10 = scatter3(ax1, NaN, NaN, NaN,...                % 数据点的x和y坐标
    25, ...                      % 数据点大小
    'b', ...          % 数据点填充颜色（自定义颜色）
    'filled', ...                 % 填充数据点
    'MarkerEdgeColor', 'b', ...   % 数据点边缘颜色（绿色）
    'MarkerFaceAlpha', 1, ...   % 设置数据点填充颜色透明度
    'MarkerEdgeAlpha', 1);        % 设置数据点边缘颜色透明度

h101 = scatter3(ax1, NaN, NaN, NaN,...                % 数据点的x和y坐标
    15, ...                      % 数据点大小
    'b', ...          % 数据点填充颜色（自定义颜色）
    'filled', ...                 % 填充数据点
    'MarkerEdgeColor', 'b', ...   % 数据点边缘颜色（绿色）
    'MarkerFaceAlpha', 1, ...   % 设置数据点填充颜色透明度
    'MarkerEdgeAlpha', 1);        % 设置数据点边缘颜色透明度

h11 = scatter3(ax1, NaN, NaN, NaN, ...                % 数据点的x和y坐标
    25, ...                      % 数据点大小
    [183/255, 034/255, 048/255], ...          % 数据点填充颜色（自定义颜色）
    'filled', ...                 % 填充数据点
    'MarkerEdgeColor', [183/255, 034/255, 048/255], ...   % 数据点边缘颜色（绿色）
    'MarkerFaceAlpha', 0.4, ...   % 设置数据点填充颜色透明度
    'MarkerEdgeAlpha', 1);        % 设置数据点边缘颜色透明度

h13 = quiver3(ax1, NaN, NaN, NaN, NaN, NaN, NaN, ...                % 数据点的x和y坐标
        'Color', 'r', ...
        'LineWidth', 0.5, ...
        'AutoScale' ,'off', ...
        'DisplayName', 'Arrows');        % 设置数据点边缘颜色透明度


h14 = quiver3(ax1, NaN, NaN, NaN, NaN, NaN, NaN, ...                % 数据点的x和y坐标
    'Color', 'b', ...
    'LineWidth', 0.5, ...
    'AutoScale' ,'off', ...
    'DisplayName', 'Arrows1');        % 设置数据点边缘颜色透明度


h12 = scatter3(ax1, NaN, NaN, NaN, ...                % 数据点的x和y坐标
    25, ...                      % 数据点大小
    [183/255, 034/255, 048/255], ...          % 数据点填充颜色（自定义颜色）
    'filled', ...                 % 填充数据点
    'MarkerEdgeColor', [183/255, 034/255, 048/255], ...   % 数据点边缘颜色（绿色）
    'MarkerFaceAlpha', 0.4, ...   % 设置数据点填充颜色透明度
    'MarkerEdgeAlpha', 1);        % 设置数据点边缘颜色透明度

% 保留封闭的box
box(ax1, 'on');

% 添加图标
legend(ax1, 'hand','fingerPoint')
xlim(ax1,[-55, 55]);
ylim(ax1,[0, 255]);
zlim(ax1,[0, 140]);
% axis equal;
set(gca,'FontSize',10) % Creates axes
view([-12,14]);

hold(ax1, 'off');
% --- 雷达与环境参数 ---
radar_pos = [0, 0, 0];              % 雷达位置，设定在坐标原点
launch_distance_from_radar = 1.5;  % 球初始位置在雷达正前方1.5米

% --- 物理常量 ---
g = 9.81;               % 重力加速度 (m/s^2)
m = 0.0459;             % 高尔夫球质量 (kg)
r = 0.02135;            % 高尔夫球半径 (m)
A = pi * r^2;           % 高尔夫球横截面积 (m^2)
rho = 1.225;            % 空气密度 (kg/m^3)
Cd = 0.4;               % 阻力系数

% --- 仿真精度 ---
dt = 0.0005;            % 时间步长 (s)，0.5 ms，生成密集的轨迹点

%% 2. 高尔夫球发射参数设置
% --- 随机化发射参数 (模拟真实击球) ---
min_v0 = 65;            % 最小初始速度 (m/s) (模拟铁杆或木杆)
max_v0 = 80;            % 最大初始速度 (m/s)
min_angle = 10;         % 最小发射仰角 (度)
max_angle = 15;         % 最大发射仰角 (度)
min_azimuth = -2.0;     % 最小水平偏角 (度)，负数偏左
max_azimuth = 2.0;      % 最大水平偏角 (度)，正数偏右

% --- 初始化球体 ---
ball = struct('id', 1, 'pos', [], 'vel', [], 'path', [], 'is_active', false);

% --- 计算初始位置和速度 ---
fprintf('----------- 高尔夫球发射参数 -----------\n');

 % 初始化图形窗口和子图
figure;

% 第一个子图：三维轨迹
subplot(3,3,[1,2,3]);
h_traj = plot3(NaN, NaN, NaN, 'b-');
grid on;
xlabel('X');
ylabel('Y');
zlabel('Z');
xlim([-55, 55]);
title('Ball Trajectory');
hold on; % 保持图形以便后续更新

% 第二个子图：Vx 随时间变化
subplot(3,3,4);
h_vx = plot(0, NaN, 'r-');
xlabel('Time (s)');
ylabel('Vx (m/s)');
title('Velocity X Component');
grid on;
hold on;

% 第三个子图：Vy 随时间变化
subplot(3,3,5);
h_vy = plot(0, NaN, 'g-');
xlabel('Time (s)');
ylabel('Vy (m/s)');
title('Velocity Y Component');
grid on;
hold on;

% 第四个子图：Vz 随时间变化
subplot(3,3,6); % 重新调整子图布局，让Vx,Vy,Vz可以并排
% 创建新的子图，或者调整现有子图位置，确保Vz有独立区域
% 为了方便演示，这里我将Vx,Vy,Vz放在同一行，Vz在Vx,Vy下方，但这可能需要根据你的喜好调整
% 如果希望Vz也独立一行，可以考虑subplot(3,1,3) 或者更复杂的布局
% 这里我将Vz与Vx, Vy放在同一行，但显示在下方。实际应用中，你可能需要调整布局。
h_vz = plot(0, NaN, 'b-');
xlabel('Time (s)');
ylabel('Vz (m/s)');
title('Velocity Z Component');
grid on;
hold on;

% 第五个子图：合速度大小随时间变化 (占据第三行)
subplot(3,3,[7,8,9]); % 3x3 网格，占据第 7, 8, 9 位置
h_v_mag = plot(NaN, NaN, 'k-', 'LineWidth', 1.5); % 黑色线条
grid on;
xlabel('Time, s');
ylabel('Total Speed, m/s');
title('V mag Component');
hold on;

% 初始化时间数组
c = 0;
% 初始位置：雷达位置 + 正前方距离 (沿Y轴)
ball.pos = radar_pos' + [0; launch_distance_from_radar; 0]; 
fprintf('发射位置 (X,Y,Z): (%.2f, %.2f, %.2f) m\n', ball.pos(1), ball.pos(2), ball.pos(3));

% 随机生成发射参数
v0_rand = min_v0 + (max_v0 - min_v0) * rand();
elevation_deg = min_angle + (max_angle - min_angle) * rand();
azimuth_deg = min_azimuth + (max_azimuth - min_azimuth) * rand();

elevation_rad = deg2rad(elevation_deg);
azimuth_rad = deg2rad(azimuth_deg);

fprintf('发射速度: %.2f m/s\n', v0_rand);
fprintf('发射仰角: %.2f 度\n', elevation_deg);
fprintf('水平偏角: %.2f 度\n', azimuth_deg);
fprintf('------------------------------------------\n\n');

% 使用三角函数将速度分解到X, Y, Z三个轴
v_xy = v0_rand * cos(elevation_rad); % 速度在水平面的投影
v_z = v0_rand * sin(elevation_rad);  % 垂直速度分量

v_x = v_xy * sin(azimuth_rad);       % 水平速度的X分量
v_y = v_xy * cos(azimuth_rad);       % 水平速度的Y分量

ball.vel = [v_x; v_y; v_z];           % 最终的速度矢量

%% 3. 轨迹仿真主循环
ball.is_active = true;
ball.path = ball.pos; % 记录第一个点
% 初始化时间数组
time_points = 0;

% 初始化速度历史记录
vel_history = ball.vel;
v_mag_history = norm(ball.vel); % <--- 新增：初始化合速度历史记录
while ball.is_active
    % --- 计算物理量 ---
    v_mag = norm(ball.vel);
    F_drag = -0.5 * rho * A * Cd * v_mag * ball.vel; % 阻力
    F_net = [0; 0; -m*g] + F_drag;                 % 合力
    a = F_net / m;                                  % 加速度
    
    % --- 更新速度和位置 (欧拉法) ---
    ball.vel = ball.vel + a * dt;
    
    ball.pos = ball.pos + ball.vel * dt;
    
    % --- 记录轨迹点 ---
    ball.path = [ball.path, ball.pos];
    
    % --- 记录速度历史 ---
    vel_history = [vel_history, ball.vel];
    v_mag_history = [v_mag_history, v_mag]; % <--- 新增：记录当前合速度
    % --- 更新时间点 ---
    time_points = [time_points, time_points(end) + dt];
    
    % --- 动态绘图 ---
    set(h_traj, 'XData', ball.path(1,:), 'YData', ball.path(2,:), 'ZData', ball.path(3,:));
    set(h_vx, 'XData', time_points, 'YData', vel_history(1,:));
    set(h_vy, 'XData', time_points, 'YData', vel_history(2,:));
    set(h_vz, 'XData', time_points, 'YData', vel_history(3,:));
    set(h_v_mag, 'XData', time_points, 'YData', v_mag_history); % <--- 新增：更新合速度图

    drawnow limitrate; % 限制刷新率以提高性能
    
    % --- 检查是否落地 (Z坐标小于0) ---
    if ball.pos(3) < 0
        ball.is_active = false;
    end
end

fprintf('仿真完成，轨迹总时长: %.2f s\n', size(ball.path, 2) * dt);

% 在仿真结束后，确保所有hold on都被关闭，如果需要的话
hold off;

%% 4. 结果提取与可视化
% --- 从 ball.path 结构中提取最终的坐标数据 ---
trajectory_data = ball.path;
x_traj = trajectory_data(1, :);
y_traj = trajectory_data(2, :);
z_traj = trajectory_data(3, :);

fprintf('轨迹数据已保存在变量 x_traj, y_traj, z_traj 中。\n');
fprintf('轨迹共包含 %d 个数据点。\n', length(x_traj));



SimulationTime = size(ball.path, 2) * dt; % 这个数据集采样速率就是100hz， 1.5是缩放因子，用于对比较快的书写进行缓慢一点
fmcw.NumOfFrame = floor(SimulationTime/fmcw.TimeOfFrame);
% 插值的点至少要大于num_total_interp，才能保证每个chirp的对于目标位置是不一样的（是移动得了）
num_total_interp = fmcw.NumOfFrame * fmcw.NumOfChirpInOneFrame * fmcw.numberOfTransmitAntennas;




tLen = 0:1:length(x_traj)-1;
%关键，为了仿真出微多普勒特征，基本每个chirp的采样到点位置不一样才能体现出速度变化插值的点一定要多
t_interp = linspace(0, length(x_traj), num_total_interp);
% 对 x, y 进行线性插值
x_interp = interp1(tLen, x_traj, t_interp, 'spline');
y_interp = interp1(tLen, y_traj, t_interp, 'spline');
z_interp = interp1(tLen, z_traj, t_interp, 'spline');

% 5. 在3D空间中相对于天线参考点进行定位
% 书写X坐标在雷达坐标系中

%% 目标设置

trajectory = [x_interp;y_interp;z_interp]';%运动路径，速度符合sigma对数正态分布的
% set(h10, 'XData', handmodel.trajectory(:,1), 'YData', handmodel.trajectory(:,2) , 'ZData', handmodel.trajectory(:,3), 'MarkerFaceAlpha', 0.05,'MarkerEdgeAlpha', 0.05);

    % 找到采样的点时间轴
tt = [];
for frame=1:fmcw.NumOfFrame
    for chirp=1:fmcw.NumOfChirpInOneFrame
        t = (frame-1)*fmcw.TimeOfFrame + (chirp-1)*(fmcw.chirpTRandEndime + fmcw.Tidle) * fmcw.numberOfTransmitAntennas + ...
            [0,fmcw.chirpTRandEndime+fmcw.Tidle];%这里这样默认是2个发射天线了
        tt = [tt,t];
    end
end


vectTraj = trajectory-trajectory(1,:);
indexList = floor(tt./max(tt)*length(vectTraj));
y_rx = zeros(fmcw.NumOfFrame, fmcw.numberOfvirtueRxannate, fmcw.NumOfChirpInOneFrame, fmcw.numadcInPerChirp);
%     disantenna(ax1,fmcw.txPos, fmcw.rxPos);
RawDataMatrix = zeros(fmcw.NumOfFrame, fmcw.NumOfChirpInOneFrame, fmcw.numberOfvirtueRxannate, fmcw.numadcInPerChirp);

%     set(h101, 'XData', (x-mean(x))./2000, 'YData', zeros(1,length(x)) , 'ZData', 0.40-y./2000, 'MarkerFaceAlpha', 0.5,'MarkerEdgeAlpha', 0.5);
% 泊松分布的lambda参数，即期望的杂波点数量。
% 一个较小的值 (如 0.5) 意味着杂波出现的概率较低。
clutter_lambda = 0.0; 
% 新增：定义杂波状态更新的概率 (例如 0.3 表示有30%的概率在每帧开始时更新杂波)
clutter_update_prob = 0.0; 
% 定义杂波可能出现的空间范围 [xmin, xmax; ymin, ymax; zmin, zmax]
clutter_area_bounds = [-45, 45;  % X轴范围 (米)
                       0, 400;  % Y轴范围 (米)
                       0, 245];   % Z轴范围 (米)
% 2. 新增：多径杂波参数
multipath_lambda = 0.0; % 多径杂波的期望数量
multipath_position_std = 10; % 多径杂波位置相对目标的高斯噪声标准差(米)
% 初始化一个变量，用于存储当前帧的杂波位置
persistent_clutter_positions = []; 
persistent_multipath_positions = []; % 新增：用于存储多径杂波
index = 1;
for i = 1:length(indexList)-1
    cart_v_plot = trajectory(1,:) + vectTraj(indexList(i)+1, :);
    frameIndex = floor((i-1)/fmcw.numberOfTransmitAntennas/fmcw.NumOfChirpInOneFrame)+1;
    chirpIndex = mod(floor((i-1)/fmcw.numberOfTransmitAntennas),fmcw.NumOfChirpInOneFrame)+1;
    TxIndex = (1:4)+4*(mod((i-1),2));

    % --- 新增：动态杂波生成模块 ---
    % 根据您的要求，杂波在每帧的第一个Chirp时进行更新并且满足随机更新概率时，才更新杂波
    if chirpIndex == 1 && TxIndex(1)==1 && rand() < clutter_update_prob
        % --- a. 生成背景杂波 ---
        num_clutter_points = poissrnd(clutter_lambda);
        if num_clutter_points > 0
            clutter_x = clutter_area_bounds(1,1) + (clutter_area_bounds(1,2)-clutter_area_bounds(1,1)) * rand(num_clutter_points, 1);
            clutter_y = clutter_area_bounds(2,1) + (clutter_area_bounds(2,2)-clutter_area_bounds(2,1)) * rand(num_clutter_points, 1);
            clutter_z = clutter_area_bounds(3,1) + (clutter_area_bounds(3,2)-clutter_area_bounds(3,1)) * rand(num_clutter_points, 1);
            persistent_clutter_positions = [clutter_x, clutter_y, clutter_z];
        else
            persistent_clutter_positions = [];
        end

        % --- b. 新增：生成多径杂波 ---
        num_multipath_points = poissrnd(multipath_lambda);
        if num_multipath_points > 0
            % 获取当前帧的目标位置作为多径的中心
            target_mean_position = cart_v_plot(index,:);
            % 生成高斯噪声，标准差由 multipath_position_std 控制
            multipath_offsets = multipath_position_std * randn(num_multipath_points, 3);
            multipath_offsets(:, 2) = 0;
            % 将噪声添加到重复的目标位置上，形成多径杂波点
            persistent_multipath_positions = repmat(target_mean_position, num_multipath_points, 1) + multipath_offsets;
        else
            persistent_multipath_positions = [];
        end
    end
    
    % --- 整合目标与所有杂波 ---
    target_position = cart_v_plot(index,:);
    % 将目标、背景杂波、多径杂波合并成一个点集
    all_points_for_calc = [target_position; persistent_clutter_positions; persistent_multipath_positions];
    
    amp_vect = fmcw.calculate_amp_vect(fmcw.txPos(mod(i-1,2)+1,:), fmcw.rxPos, all_points_for_calc);

    % 计算接收信号
    y_rx = sum(amp_vect, 3);
    
    % --- 新增：仿真直流耦合 (DC Offset) ---
    % 产生一个固定的复数直流偏移量。为了使其更真实，我们可以给它一个随机但固定的相位。
    % 这个直流分量会被加到每个采样点上。
    dc_offset_amplitude = 4.0;
    dc_offset_phase = 2 * pi * rand(); % 为每次计算引入一个随机相位
    dc_offset_component = dc_offset_amplitude * exp(1j * dc_offset_phase);
    
    % 将直流分量、目标信号和杂波信号叠加
    y_rx_with_dc = y_rx + dc_offset_component;

    % 将最终信号存入数据矩阵
    RawDataMatrix(frameIndex,chirpIndex,TxIndex,:) = y_rx_with_dc;
    if chirpIndex == 1 && TxIndex(1)==1
        set(h12, 'XData', [get(h12,'XData') cart_v_plot(index,1)], 'YData', [get(h12,'YData') cart_v_plot(index,2)], 'ZData', [get(h12,'ZData') cart_v_plot(index,3)]);
        set(h10, 'XData', all_points_for_calc(:,1), 'YData', all_points_for_calc(:,2), 'ZData', all_points_for_calc(:,3));

        pause(0.0001)
    end
end
% 1. 维度转换
% 原始数据维度: [frame, chirp, Rx, sample]
% 转换后维度:   [frame, chirp, sample, Rx]
RawDataMatrix = permute(RawDataMatrix, [1, 2, 4, 3]);

% 2. 添加加性高斯白噪声 (AWGN)
% 定义加性噪声的功率
noise_power = 0.0001;
% 创建与RawDataMatrix同样大小的复数噪声矩阵
% 噪声功率被均分到实部和虚部

noise_real = sqrt(noise_power/2) * randn(size(RawDataMatrix));
noise_imag = sqrt(noise_power/2) * randn(size(RawDataMatrix));
additive_noise = complex(noise_real, noise_imag);

% 将复数矩阵降为单精度
RawDataMatrix = single(RawDataMatrix + additive_noise);

% 保存
save('data_reduced_precision.mat', 'RawDataMatrix', '-v7.3');
